"use client";
import React, {
  useContext,
  useMemo,
  useCallback,
  memo,
  useRef,
  useState,
  useEffect,
} from "react";
import { Box, Typography, styled } from "@mui/material";
import { StoreContext } from "../../store";
import { observer } from "mobx-react";
import {
  formatTime,
  calculateTimelinePosition,
  getTimelineContainerWidth,
  TIMELINE_CONSTANTS,
} from "../../utils/timeUtils";

// 样式组件
const MarkerContainer = styled(Box)(({ theme }) => ({
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  height: "42px",
  paddingLeft: "0px",
  pointerEvents: "auto",
  zIndex: 30,
  cursor: "pointer",
}));

const Marker = styled(Box)(({ theme }) => ({
  position: "absolute",
  width: "1px",
  height: "6px",
  backgroundColor: theme.palette.grey[600],
  top: "12px",
  transition: "height 0.2s ease",
  "&:hover": {
    height: "8px",
  },
}));

const MajorMarker = styled(Marker)(({ theme }) => ({
  height: "12px",
  width: "1px",
  color: theme.palette.text.secondary,
  top: "8px",
  borderRadius: "1px",
}));

const MarkerLabel = styled(Typography)(({ theme }) => ({
  position: "absolute",
  fontSize: "0.7rem",
  fontWeight: 500,
  color: theme.palette.text.secondary,
  transform: "translateX(-50%)",
  whiteSpace: "nowrap",
  userSelect: "none",
  opacity: 0.9,
  transition: "opacity 0.2s ease, transform 0.2s ease",
}));

// 提取常量避免重复创建
const POSSIBLE_INTERVALS: readonly number[] = [
  100, 250, 500, 1000, 2000, 5000, 10000, 15000, 30000, 60000, 120000, 180000,
  300000, 600000, 900000, 1800000, 3600000, 7200000, 10800000, 18000000,
  86400000,
];

// 缓存间隔配置的计算结果
const intervalConfigCache = new Map<
  number,
  { majorInterval: number; minorInterval: number; minorMarkersCount: number }
>();

// 优化间隔计算函数
const calculateIntervals = (timelineDisplayDuration: number) => {
  // 检查缓存
  const cached = intervalConfigCache.get(timelineDisplayDuration);
  if (cached) return cached;

  const minMajorMarkers = 5;
  const maxMajorMarkers = 15;
  const optimalMajorMarkers = 8;

  const idealInterval = timelineDisplayDuration / optimalMajorMarkers;
  let bestInterval = POSSIBLE_INTERVALS[0];
  let bestScoreForDensity = Number.MAX_SAFE_INTEGER;

  // 优化：使用for循环代替for...of以获得更好的性能
  for (let i = 0; i < POSSIBLE_INTERVALS.length; i++) {
    const interval = POSSIBLE_INTERVALS[i];
    const markerCount = Math.ceil(timelineDisplayDuration / interval);

    if (markerCount >= minMajorMarkers && markerCount <= maxMajorMarkers) {
      const densityScore = Math.abs(interval - idealInterval);
      if (densityScore < bestScoreForDensity) {
        bestScoreForDensity = densityScore;
        bestInterval = interval;
      }
    }
  }

  if (bestScoreForDensity === Number.MAX_SAFE_INTEGER) {
    bestInterval = POSSIBLE_INTERVALS.reduce((prev, curr) =>
      Math.abs(curr - idealInterval) < Math.abs(prev - idealInterval)
        ? curr
        : prev
    );
  }

  // 优化次要标记数量计算
  let minorMarkersPerMajor: number;
  if (bestInterval <= 500) {
    minorMarkersPerMajor = 1;
  } else if (bestInterval <= 1000) {
    minorMarkersPerMajor = 2;
  } else if (bestInterval <= 60000) {
    minorMarkersPerMajor = 4;
  } else {
    minorMarkersPerMajor = 5;
  }

  const result = {
    majorInterval: bestInterval,
    minorInterval: bestInterval / (minorMarkersPerMajor + 1),
    minorMarkersCount: minorMarkersPerMajor,
  };

  // 缓存结果
  intervalConfigCache.set(timelineDisplayDuration, result);
  return result;
};

// 使用memo优化单个标记的渲染
const TimeMarker = memo(
  ({
    position,
    isMajor,
    time,
  }: {
    position: string;
    isMajor: boolean;
    time: number;
  }) => (
    <>
      {isMajor ? (
        <>
          <MajorMarker
            style={{ left: position }}
            className="time-scale-marker"
          />
          <MarkerLabel
            style={{ left: position, top: "22px" }}
            className="time-scale-marker-label"
          >
            {formatTime(time)}
          </MarkerLabel>
        </>
      ) : (
        <Marker style={{ left: position }} className="time-scale-marker" />
      )}
    </>
  )
);

TimeMarker.displayName = "TimeMarker";

// 导出组件
export const TimeScaleMarkers = observer(() => {
  const store = useContext(StoreContext);
  const { HANDLE_WIDTH } = TIMELINE_CONSTANTS;

  const containerRef = useRef<HTMLDivElement | null>(null);
  const [containerWidth, setContainerWidth] = useState<number | null>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  // 优化ResizeObserver的使用
  useEffect(() => {
    setContainerWidth(getTimelineContainerWidth());

    // 避免重复创建ResizeObserver
    if (!resizeObserverRef.current) {
      resizeObserverRef.current = new ResizeObserver(() => {
        setContainerWidth(getTimelineContainerWidth());
      });
    }

    const container = document.querySelector(".timeline-container");
    if (container && resizeObserverRef.current) {
      resizeObserverRef.current.observe(container);
    }

    return () => {
      if (container && resizeObserverRef.current) {
        resizeObserverRef.current.unobserve(container);
      }
    };
  }, []);

  // 清理ResizeObserver
  useEffect(() => {
    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
    };
  }, []);

  // 缓存间隔配置
  const intervalConfig = useMemo(
    () => calculateIntervals(store.timelineDisplayDuration),
    [store.timelineDisplayDuration]
  );

  // 优化点击处理器
  const handleMarkerClick = useCallback(
    (event: React.MouseEvent) => {
      const container = event.currentTarget as HTMLElement;
      const rect = container.getBoundingClientRect();
      const clickX = event.clientX - rect.left;
      const contentWidth = container.clientWidth - HANDLE_WIDTH;
      const clickPosition = Math.max(0, clickX - HANDLE_WIDTH) / contentWidth;

      let clickTimeMs =
        store.timelinePan.offsetX +
        clickPosition * store.timelineDisplayDuration;

      if (store.maxDuration > 0) {
        clickTimeMs = Math.min(clickTimeMs, store.maxDuration);
      }

      store.clearAllSelections();
      store.handleSeek(clickTimeMs);
    },
    [store, HANDLE_WIDTH]
  );

  // 大幅优化标记生成逻辑
  const markers = useMemo(() => {
    if (!containerWidth) return [];

    const { majorInterval, minorMarkersCount } = intervalConfig;
    const visibleStartTime = Math.max(0, store.timelinePan.offsetX);
    const visibleEndTime = visibleStartTime + store.timelineDisplayDuration;
    const firstMajorMarkerTime =
      Math.floor(visibleStartTime / majorInterval) * majorInterval;
    const markerPadding = HANDLE_WIDTH;

    const allMarkers: Array<{
      time: number;
      position: string;
      isMajor: boolean;
    }> = [];

    // 单次循环生成所有标记
    for (
      let majorTime = firstMajorMarkerTime;
      majorTime <= visibleEndTime;
      majorTime += majorInterval
    ) {
      if (majorTime < 0) continue;

      // 添加主要标记
      const majorPosition = calculateTimelinePosition(
        majorTime,
        store.timelineDisplayDuration,
        store.timelinePan.offsetX,
        containerWidth
      );

      if (majorPosition >= -5 && majorPosition <= 105) {
        allMarkers.push({
          time: majorTime,
          position: `calc(${majorPosition}% + ${markerPadding}px)`,
          isMajor: true,
        });
      }

      // 在同一循环中生成次要标记
      const minorStep = majorInterval / (minorMarkersCount + 1);
      for (let i = 1; i <= minorMarkersCount; i++) {
        const minorTime = majorTime + i * minorStep;
        if (minorTime > visibleEndTime || minorTime < 0) continue;

        const minorPosition = calculateTimelinePosition(
          minorTime,
          store.timelineDisplayDuration,
          store.timelinePan.offsetX,
          containerWidth
        );

        if (minorPosition >= -5 && minorPosition <= 105) {
          allMarkers.push({
            time: minorTime,
            position: `calc(${minorPosition}% + ${markerPadding}px)`,
            isMajor: false,
          });
        }
      }
    }

    // 优化排序：只在需要时排序，并且使用更快的排序方法
    return allMarkers.sort((a, b) => a.time - b.time);
  }, [
    containerWidth,
    intervalConfig,
    store.timelineDisplayDuration,
    store.timelinePan.offsetX,
    HANDLE_WIDTH,
  ]);

  return (
    <MarkerContainer onClick={handleMarkerClick}>
      <Box
        sx={{
          position: "absolute",
          // bgcolor: "grey.100",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: -1,
        }}
      />
      {markers.map((marker) => (
        <TimeMarker
          key={marker.time}
          position={marker.position}
          isMajor={marker.isMajor}
          time={marker.time}
        />
      ))}
    </MarkerContainer>
  );
});
