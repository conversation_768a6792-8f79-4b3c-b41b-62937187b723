"use client";
import { Box, useTheme } from "@mui/material";
import { observer } from "mobx-react";
import React, {
  useCallback,
  useContext,
  useLayoutEffect,
  useRef,
  useState,
  useMemo,
} from "react";
import { StoreContext } from "../../store";
import { Caption } from "../../types";
import {
  formatTime,
  getTimelineContainerWidth,
  timeStringToMs,
} from "../../utils/timeUtils";
import { CaptionItem } from "./caption/CaptionItem";
import { CaptionsTrackViewProps } from "./caption/types/types";
import { CAPTION_HEIGHT } from "./styles";
import CaptionTrackContextMenu from "./menu/CaptionTrackContextMenu";

// 组件类型定义
interface CaptionContentProps {
  caption: Caption;
  handleClick: (e: React.MouseEvent) => void;
  handleDoubleClick: (e: React.MouseEvent) => void;
}

// 主字幕轨道视图组件
export const CaptionsTrackView = observer(
  ({ captions }: CaptionsTrackViewProps) => {
    const store = useContext(StoreContext);
    const theme = useTheme();
    const trackRef = useRef<HTMLDivElement>(null);
    const [containerWidth, setContainerWidth] = useState<number | null>(null);
    const [contextMenu, setContextMenu] = useState<{
      mouseX: number;
      mouseY: number;
    } | null>(null);

    useLayoutEffect(() => {
      const updateWidth = () => {
        // 使用统一的容器宽度获取函数
        setContainerWidth(getTimelineContainerWidth());
      };

      updateWidth();
      window.addEventListener("resize", updateWidth);

      return () => window.removeEventListener("resize", updateWidth);
    }, []);

    // 可见时间范围计算 - 用于字幕可见性优化（与TrackView保持一致）
    const visibleTimeRange = useMemo(() => {
      const visibleStart = store.timelinePan.offsetX;
      const visibleEnd = visibleStart + store.timelineDisplayDuration;

      // 添加缓冲区域，扩展可见范围（与TrackView保持一致的10%缓冲）
      // 左右各扩展10%的显示时长，确保滚动时字幕能提前加载
      const bufferTime = store.timelineDisplayDuration * 0.1;

      return {
        start: Math.max(0, visibleStart - bufferTime),
        end: visibleEnd + bufferTime,
      };
    }, [store.timelinePan.offsetX, store.timelineDisplayDuration]);

    // 过滤可见字幕 - 使用宽松的可见性检查（与TrackView保持一致）
    const visibleCaptions = useMemo(() => {
      if (!captions || captions.length === 0 || containerWidth === null) {
        return [];
      }

      return captions.filter((caption) => {
        const captionStartMs = timeStringToMs(caption.startTime);
        const captionEndMs = timeStringToMs(caption.endTime);

        // 更宽松的检查：字幕是否与扩展的可见时间范围有交集
        // 即使字幕只有一小部分在可见范围内也会被渲染
        return (
          captionEndMs > visibleTimeRange.start &&
          captionStartMs < visibleTimeRange.end
        );
      });
    }, [captions, containerWidth, visibleTimeRange]);

    // 右键菜单处理函数
    const handleContextMenu = useCallback((e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();

      // 显示右键菜单
      setContextMenu({
        mouseX: e.clientX,
        mouseY: e.clientY,
      });
    }, []);

    const handleCloseContextMenu = useCallback(() => {
      setContextMenu(null);
    }, []);

    if (!captions || captions.length === 0) {
      return (
        <>
          <Box
            ref={trackRef}
            onContextMenu={handleContextMenu}
            sx={{
              position: "relative",
              width: "100%",
              height: CAPTION_HEIGHT,
              my: 1,
              bgcolor: "grey.50",
              borderRadius: 1,
              cursor: "context-menu",
            }}
          />

          <CaptionTrackContextMenu
            contextMenu={contextMenu}
            handleClose={handleCloseContextMenu}
          />
        </>
      );
    }

    const handleTimeFrameChange = useCallback(
      (caption: Caption, start: number, end: number) => {
        const formattedStart = formatTime(start);
        const formattedEnd = formatTime(end);

        const startTimeFormatted = store.formatCaptionTime(formattedStart);
        const endTimeFormatted = store.formatCaptionTime(formattedEnd);

        store.updateCaption(caption.id, "startTime", startTimeFormatted);
        store.updateCaption(caption.id, "endTime", endTimeFormatted);
      },
      [store]
    );

    return (
      <>
        <Box
          ref={trackRef}
          onContextMenu={handleContextMenu}
          sx={{
            position: "relative",
            width: "100%",
            left: "10px",
            height: CAPTION_HEIGHT,
            my: 0.5, // 减小字幕轨道的上下边距
            bgcolor: "grey.100",
            borderRadius: 1,
            px: 0.5,
            overflow: "visible",
            cursor: "context-menu",
          }}
        >
          {visibleCaptions.map((caption) => (
            <CaptionItem
              key={caption.id}
              caption={caption}
              containerWidth={containerWidth}
              handleTimeFrameChange={handleTimeFrameChange}
              allCaptions={captions}
            />
          ))}
        </Box>

        <CaptionTrackContextMenu
          contextMenu={contextMenu}
          handleClose={handleCloseContextMenu}
        />
      </>
    );
  }
);
